#!/usr/bin/env python3
"""
测试缓冲区分离问题修复
专门测试 "ValueError: underlying buffer has been detached" 问题
"""
import os
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def test_buffer_detached_fix():
    """测试缓冲区分离问题修复"""
    print("=" * 60)
    print("测试缓冲区分离问题修复")
    print("=" * 60)
    
    try:
        # 导入日志模块
        from core.logger import log
        
        print("✅ 成功导入日志模块")
        
        # 测试大量日志输出，模拟可能导致缓冲区分离的情况
        print("\n📝 测试大量日志输出...")
        
        for i in range(50):
            log.info(f"测试日志 {i}: 这是一条测试日志，用于验证缓冲区分离问题是否修复")
            if i % 10 == 0:
                log.warning(f"警告日志 {i}: 这是一条警告日志")
            if i % 15 == 0:
                log.error(f"错误日志 {i}: 这是一条错误日志")
            
            # 短暂延迟，模拟实际使用场景
            time.sleep(0.01)
        
        print("✅ 大量日志输出测试完成")
        
        # 测试复杂对象日志
        print("\n📝 测试复杂对象日志...")
        
        complex_data = {
            'timestamp': datetime.now(),
            'elapsed': time.time(),
            'data': list(range(100)),
            'nested': {'key': 'value', 'number': 42}
        }
        
        log.info(f"复杂对象日志: {complex_data}")
        
        print("✅ 复杂对象日志测试完成")
        
        # 测试异常情况
        print("\n📝 测试异常情况...")
        
        try:
            # 模拟可能导致问题的操作
            raise ValueError("这是一个测试异常")
        except Exception as e:
            log.error(f"捕获到异常: {e}")
        
        print("✅ 异常情况测试完成")
        
        print("\n🎉 缓冲区分离问题修复测试完成！")
        print("如果没有看到 'ValueError: underlying buffer has been detached' 错误，")
        print("说明问题已经修复！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_base_page_simulation():
    """模拟 BasePage 的使用场景"""
    print("\n" + "=" * 40)
    print("模拟 BasePage 使用场景")
    print("=" * 40)
    
    try:
        from core.logger import log
        
        # 模拟 BasePage 初始化
        app_name = "ella"
        page_name = "floating_page"
        
        print("模拟 BasePage 初始化...")
        log.info(f"初始化页面: {app_name} - {page_name}")
        
        # 模拟页面操作
        operations = [
            "尝试打开Ella浮窗",
            "方法1: 尝试通过长按power键唤起Ella浮窗",
            "模拟长按power键 3.0秒",
            "✅ 长按power键命令执行成功",
            "✅ 通过长按power键成功打开浮窗",
            "在浮窗中执行文本命令: Hello Ella",
            "当前输入模式: voice",
            "检测到键盘按钮，当前处于语音模式，需要切换到文本模式",
            "✅ 成功切换到文本输入模式",
            "✅ 通过发送按钮发送命令成功"
        ]
        
        for i, operation in enumerate(operations):
            log.info(operation)
            time.sleep(0.1)  # 模拟操作间隔
            
            if i % 3 == 0:
                log.debug(f"调试信息: 执行第 {i+1} 个操作")
        
        print("✅ BasePage 使用场景模拟完成")
        
    except Exception as e:
        print(f"❌ BasePage 模拟失败: {e}")


def test_concurrent_logging():
    """测试并发日志记录"""
    print("\n" + "=" * 40)
    print("测试并发日志记录")
    print("=" * 40)
    
    try:
        import threading
        from core.logger import log
        
        def worker_thread(thread_id):
            """工作线程函数"""
            for i in range(20):
                log.info(f"线程 {thread_id} - 消息 {i}")
                if i % 5 == 0:
                    log.warning(f"线程 {thread_id} - 警告 {i}")
                time.sleep(0.01)
        
        # 创建多个线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print("✅ 并发日志记录测试完成")
        
    except Exception as e:
        print(f"❌ 并发日志测试失败: {e}")


if __name__ == "__main__":
    test_buffer_detached_fix()
    test_base_page_simulation()
    test_concurrent_logging()
    
    print("\n" + "=" * 60)
    print("缓冲区分离问题修复验证完成")
    print("如果没有看到任何 'Record was:' 或 'buffer has been detached' 错误，")
    print("说明问题已彻底解决！")
    print("=" * 60)
