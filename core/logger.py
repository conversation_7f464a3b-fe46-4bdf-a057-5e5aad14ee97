"""
日志模块 - 优化版本
基于loguru实现的分类日志系统，支持日志轮转和分类管理
"""
import os
import sys
from datetime import datetime
from loguru import logger
from utils.yaml_utils import YamlUtils
from utils.file_utils import FileUtils


class Logger:
    """优化的日志管理类"""

    _instance = None
    _initialized = False
    _loggers = {}  # 存储不同类型的日志器

    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化日志配置"""
        if not self._initialized:
            self._setup_logger()
            Logger._initialized = True
    
    def _setup_logger(self):
        """设置优化的日志配置"""
        try:
            # 加载配置
            config_path = YamlUtils.get_config_path("config.yaml")
            config = YamlUtils.load_yaml(config_path)
            log_config = config.get("logging", {})

            # 移除默认处理器
            logger.remove()

            # 设置全局异常处理，防止记录对象泄露
            self._setup_global_exception_handler()

            # 重定向可能的记录对象输出
            self._patch_record_output()

            # 获取项目根目录
            project_root = YamlUtils.get_project_root()

            # 创建日志目录结构
            self._create_log_directories(project_root)

            # 日志格式
            log_format = log_config.get("format",
                "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}")

            # 日志级别
            log_level = log_config.get("level", "INFO")

            # 添加控制台处理器 - 使用更安全的配置，解决 Windows 缓冲区分离问题
            try:
                # 使用 stderr 而不是 stdout，更稳定
                import sys
                import io

                # 创建一个安全的输出流
                safe_stream = self._create_safe_stream()

                logger.add(
                    safe_stream,
                    format=log_format,
                    level=log_level,
                    colorize=False,  # 禁用颜色，避免缓冲区问题
                    filter=self._console_filter,
                    catch=True,  # 捕获异常，防止日志系统崩溃
                    backtrace=False,  # 禁用回溯，避免输出内部信息
                    diagnose=False,  # 禁用诊断，避免输出敏感信息
                )
            except Exception as e:
                # 如果控制台处理器添加失败，只使用文件日志
                pass

            # 设置分类日志处理器
            self._setup_categorized_loggers(project_root, log_config, log_format, log_level)

        except Exception as e:
            # 如果配置加载失败，使用默认配置
            logger.add(
                sys.stdout,
                level="INFO",
                catch=True,
                backtrace=False,
                diagnose=False
            )
            logger.error(f"日志配置加载失败，使用默认配置: {e}")

    def _create_log_directories(self, project_root: str):
        """创建日志目录结构"""
        log_dirs = [
            "logs/general",      # 通用日志
            "logs/test",         # 测试日志
            "logs/debug",        # 调试日志
            "logs/error",        # 错误日志
            "logs/performance",  # 性能日志
            "logs/archive"       # 归档日志
        ]

        for log_dir in log_dirs:
            full_path = os.path.join(project_root, log_dir)
            FileUtils.ensure_dir(full_path)

    def _setup_global_exception_handler(self):
        """设置全局异常处理器，防止记录对象泄露"""
        import sys

        # 保存原始的 excepthook
        original_excepthook = sys.excepthook

        def safe_excepthook(exc_type, exc_value, exc_traceback):
            """安全的异常处理器，防止记录对象输出"""
            try:
                # 检查异常是否与日志记录相关
                if "Record was:" in str(exc_value):
                    # 静默处理日志记录对象泄露
                    return

                # 对于其他异常，使用原始处理器
                original_excepthook(exc_type, exc_value, exc_traceback)
            except Exception:
                # 如果异常处理器本身出错，静默处理
                pass

        # 设置新的异常处理器
        sys.excepthook = safe_excepthook

    def _patch_record_output(self):
        """补丁记录对象输出，防止 'Record was:' 输出"""
        import builtins

        # 保存原始的 print 函数
        original_print = builtins.print

        def safe_print(*args, **kwargs):
            """安全的 print 函数，过滤记录对象输出"""
            try:
                # 检查参数中是否包含记录对象输出
                for arg in args:
                    arg_str = str(arg)
                    if "Record was:" in arg_str and "elapsed" in arg_str and "datetime.timedelta" in arg_str:
                        # 静默处理记录对象输出
                        return

                # 对于正常输出，使用原始 print
                original_print(*args, **kwargs)
            except Exception:
                # 如果 print 本身出错，尝试使用原始 print
                try:
                    original_print(*args, **kwargs)
                except Exception:
                    # 完全静默处理
                    pass

        # 替换 print 函数
        builtins.print = safe_print

    def _create_safe_stream(self):
        """创建一个安全的输出流，解决 Windows 缓冲区分离问题"""
        import sys
        import io

        class SafeStream:
            """安全的输出流包装器"""

            def __init__(self):
                self._streams = [sys.stderr, sys.stdout]
                self._fallback = io.StringIO()

            def write(self, message):
                """安全写入消息"""
                # 尝试写入到可用的流
                for stream in self._streams:
                    try:
                        if hasattr(stream, 'write') and not stream.closed:
                            stream.write(message)
                            if hasattr(stream, 'flush'):
                                stream.flush()
                            return
                    except (ValueError, OSError, AttributeError):
                        # 缓冲区分离或其他错误，尝试下一个流
                        continue

                # 如果所有流都失败，写入到备用流
                try:
                    self._fallback.write(message)
                except Exception:
                    # 完全静默处理
                    pass

            def flush(self):
                """刷新流"""
                for stream in self._streams:
                    try:
                        if hasattr(stream, 'flush') and not stream.closed:
                            stream.flush()
                    except Exception:
                        continue

        return SafeStream()

    def _console_filter(self, record):
        """控制台日志过滤器，只显示重要信息"""
        try:
            # 只在控制台显示INFO及以上级别的日志
            return record["level"].no >= 20  # INFO=20, WARNING=30, ERROR=40
        except Exception:
            # 如果过滤器出现异常，默认不显示该记录
            return False

    def _safe_general_filter(self, record):
        """安全的通用日志过滤器"""
        try:
            message = str(record.get("message", "")).lower()
            return not any(keyword in message for keyword in ["test", "debug", "error", "performance"])
        except Exception:
            return True  # 默认允许通过

    def _safe_test_filter(self, record):
        """安全的测试日志过滤器"""
        try:
            message = str(record.get("message", "")).lower()
            name = str(record.get("name", "")).lower()
            return ("test" in message or "pytest" in name or "testcase" in name)
        except Exception:
            return False  # 默认不通过

    def _safe_debug_filter(self, record):
        """安全的调试日志过滤器"""
        try:
            level_name = record.get("level", {}).get("name", "")
            message = str(record.get("message", "")).lower()
            return (level_name == "DEBUG" or "debug" in message)
        except Exception:
            return False  # 默认不通过

    def _safe_error_filter(self, record):
        """安全的错误日志过滤器"""
        try:
            level_no = record.get("level", {}).get("no", 0)
            return level_no >= 40  # ERROR=40, CRITICAL=50
        except Exception:
            return False  # 默认不通过

    def _safe_performance_filter(self, record):
        """安全的性能日志过滤器"""
        try:
            message = str(record.get("message", "")).lower()
            keywords = ["performance", "timing", "speed", "duration", "响应时间"]
            return any(keyword in message for keyword in keywords)
        except Exception:
            return False  # 默认不通过

    def _setup_categorized_loggers(self, project_root: str, log_config: dict, log_format: str, log_level: str):
        """设置分类日志处理器"""
        today = datetime.now().strftime("%Y%m%d")

        # 日志配置 - 使用安全的过滤器函数
        log_categories = {
            "general": {
                "path": f"logs/general/app_{today}.log",
                "rotation": "5 MB",
                "retention": "10 days",
                "level": log_level,
                "filter": self._safe_general_filter
            },
            "test": {
                "path": f"logs/test/test_{today}.log",
                "rotation": "10 MB",
                "retention": "7 days",
                "level": log_level,
                "filter": self._safe_test_filter
            },
            "debug": {
                "path": f"logs/debug/debug_{today}.log",
                "rotation": "3 MB",
                "retention": "5 days",
                "level": "DEBUG",
                "filter": self._safe_debug_filter
            },
            "error": {
                "path": f"logs/error/error_{today}.log",
                "rotation": "5 MB",
                "retention": "30 days",
                "level": "ERROR",
                "filter": self._safe_error_filter
            },
            "performance": {
                "path": f"logs/performance/perf_{today}.log",
                "rotation": "2 MB",
                "retention": "14 days",
                "level": log_level,
                "filter": self._safe_performance_filter
            }
        }

        # 添加分类日志处理器
        for category, config in log_categories.items():
            try:
                log_file = os.path.join(project_root, config["path"])

                logger.add(
                    log_file,
                    format=log_format,
                    level=config["level"],
                    rotation=config["rotation"],
                    retention=config["retention"],
                    encoding="utf-8",
                    filter=config["filter"],
                    enqueue=True,  # 异步写入，提高性能
                    catch=True,  # 捕获异常，防止日志系统崩溃
                    backtrace=False,  # 禁用回溯，避免输出内部信息
                    diagnose=False,  # 禁用诊断，避免输出敏感信息
                )
            except Exception as e:
                # 如果某个文件处理器添加失败，继续添加其他的
                continue
    
    @staticmethod
    def get_logger():
        """
        获取日志实例

        Returns:
            logger: loguru日志实例
        """
        Logger()  # 确保初始化
        return logger

    @staticmethod
    def safe_log(level: str, message: str, **kwargs):
        """
        安全的日志记录方法，防止输出内部记录信息

        Args:
            level: 日志级别 (debug, info, warning, error, critical)
            message: 日志消息
            **kwargs: 其他参数
        """
        try:
            Logger()  # 确保初始化
            log_func = getattr(logger, level.lower(), logger.info)
            log_func(str(message), **kwargs)
        except Exception:
            # 如果日志记录失败，静默处理，避免影响主程序
            pass

    @staticmethod
    def get_test_logger():
        """获取测试专用日志器"""
        Logger()  # 确保初始化
        return logger.bind(category="test")

    @staticmethod
    def get_debug_logger():
        """获取调试专用日志器"""
        Logger()  # 确保初始化
        return logger.bind(category="debug")

    @staticmethod
    def get_performance_logger():
        """获取性能专用日志器"""
        Logger()  # 确保初始化
        return logger.bind(category="performance")

    @staticmethod
    def log_test_start(test_name: str):
        """记录测试开始"""
        logger.info(f"🧪 测试开始: {test_name}")

    @staticmethod
    def log_test_end(test_name: str, success: bool, duration: float = None):
        """记录测试结束"""
        status = "✅ 成功" if success else "❌ 失败"
        duration_str = f", 耗时: {duration:.2f}秒" if duration else ""
        logger.info(f"🏁 测试结束: {test_name} - {status}{duration_str}")

    @staticmethod
    def log_performance(operation: str, duration: float, details: str = ""):
        """记录性能信息"""
        logger.info(f"⚡ 性能记录: {operation} 耗时 {duration:.3f}秒 {details}")

    @staticmethod
    def log_step(step_name: str):
        """记录测试步骤"""
        logger.info(f"📋 执行步骤: {step_name}")

    @staticmethod
    def log_error_with_screenshot(error_msg: str, screenshot_path: str = None):
        """记录错误并关联截图"""
        if screenshot_path:
            logger.error(f"❌ 错误: {error_msg} | 截图: {screenshot_path}")
        else:
            logger.error(f"❌ 错误: {error_msg}")

    @staticmethod
    def cleanup_old_logs(days: int = 30):
        """清理旧日志文件"""
        try:
            project_root = YamlUtils.get_project_root()
            logs_dir = os.path.join(project_root, "logs")

            if not os.path.exists(logs_dir):
                return

            import time
            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 60 * 60)

            cleaned_count = 0
            for root, dirs, files in os.walk(logs_dir):
                for file in files:
                    if file.endswith('.log'):
                        file_path = os.path.join(root, file)
                        if os.path.getmtime(file_path) < cutoff_time:
                            try:
                                os.remove(file_path)
                                cleaned_count += 1
                            except Exception as e:
                                logger.debug(f"清理日志文件失败: {file_path}, 错误: {e}")

            if cleaned_count > 0:
                logger.info(f"🧹 清理了 {cleaned_count} 个超过 {days} 天的日志文件")

        except Exception as e:
            logger.error(f"清理日志文件时出错: {e}")


# 全局日志实例
log = Logger.get_logger()

# 专用日志实例
test_log = Logger.get_test_logger()
debug_log = Logger.get_debug_logger()
perf_log = Logger.get_performance_logger()
